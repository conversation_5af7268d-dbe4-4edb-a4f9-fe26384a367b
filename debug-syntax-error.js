const luamin = require('./luamin');

// Test with a simple script to isolate the syntax error
const simpleTestCode = `
local Players = game:GetService("Players")
local player = Players.LocalPlayer
print("Hello World")
`;

console.log("🔍 DEBUGGING SYNTAX ERROR...\n");

try {
    const obfuscated = luamin.minify(simpleTestCode);
    console.log("Obfuscated code:");
    console.log("=" .repeat(80));
    console.log(obfuscated);
    console.log("=" .repeat(80));
    
    // Check for common syntax issues
    const lines = obfuscated.split('\n');
    console.log(`\nTotal lines: ${lines.length}`);
    
    // Look for line around 1057 (if it exists)
    if (lines.length > 1057) {
        console.log(`\nLine 1057: "${lines[1056]}"`);
        console.log(`Line 1058: "${lines[1057]}"`);
        console.log(`Line 1059: "${lines[1058]}"`);
    }
    
    // Check for common syntax issues
    const issues = [];
    
    // Check for unmatched parentheses
    const openParens = (obfuscated.match(/\(/g) || []).length;
    const closeParens = (obfuscated.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
        issues.push(`Unmatched parentheses: ${openParens} open, ${closeParens} close`);
    }
    
    // Check for unmatched brackets
    const openBrackets = (obfuscated.match(/\[/g) || []).length;
    const closeBrackets = (obfuscated.match(/\]/g) || []).length;
    if (openBrackets !== closeBrackets) {
        issues.push(`Unmatched brackets: ${openBrackets} open, ${closeBrackets} close`);

        // Find bracket positions
        console.log("\n🔍 BRACKET ANALYSIS:");
        const brackets = [];
        for (let i = 0; i < obfuscated.length; i++) {
            if (obfuscated[i] === '[') brackets.push({pos: i, type: 'open', char: '['});
            if (obfuscated[i] === ']') brackets.push({pos: i, type: 'close', char: ']'});
        }

        console.log("Bracket positions:");
        brackets.forEach((bracket, index) => {
            const context = obfuscated.substring(Math.max(0, bracket.pos - 10), bracket.pos + 10);
            console.log(`  ${index + 1}. ${bracket.type} '${bracket.char}' at pos ${bracket.pos}: ...${context}...`);
        });
    }
    
    // Check for missing equals signs in assignments
    const suspiciousPatterns = [
        /local\s+\w+\s+[^=]/,
        /\w+\s+[^=]\s*\w+/,
        /function\s+\w+\s*\([^)]*\)\s*[^=]/
    ];
    
    suspiciousPatterns.forEach((pattern, index) => {
        if (pattern.test(obfuscated)) {
            issues.push(`Suspicious pattern ${index + 1} found`);
        }
    });
    
    if (issues.length > 0) {
        console.log("\n❌ POTENTIAL ISSUES FOUND:");
        issues.forEach(issue => console.log(`  - ${issue}`));
    } else {
        console.log("\n✅ No obvious syntax issues detected");
    }
    
} catch (error) {
    console.error("❌ ERROR during obfuscation:", error.message);
    console.error(error.stack);
}
